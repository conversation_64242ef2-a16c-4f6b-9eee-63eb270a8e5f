import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { StudentData } from '../api/studentApi';

interface StudentState {
  currentStudent: StudentData | null;
  isLoading: boolean;
  error: string | null;
  formData: Partial<StudentData>;
}

const initialState: StudentState = {
  currentStudent: null,
  isLoading: false,
  error: null,
  formData: {},
};

const studentSlice = createSlice({
  name: 'student',
  initialState,
  reducers: {
    setCurrentStudent: (state, action: PayloadAction<StudentData>) => {
      state.currentStudent = action.payload;
      state.error = null;
    },
    clearCurrentStudent: (state) => {
      state.currentStudent = null;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateFormData: (state, action: PayloadAction<Partial<StudentData>>) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    clearFormData: (state) => {
      state.formData = {};
    },
    setFormData: (state, action: PayloadAction<Partial<StudentData>>) => {
      state.formData = action.payload;
    },
  },
});

export const {
  setCurrentStudent,
  clearCurrentStudent,
  setLoading,
  setError,
  clearError,
  updateFormData,
  clearFormData,
  setFormData,
} = studentSlice.actions;

export default studentSlice.reducer;
