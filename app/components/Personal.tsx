'use client';

import React, { useState, useEffect, useImperativeHandle } from 'react';
import { useF<PERSON>, SubmitHandler, Controller } from 'react-hook-form';
import { FormData } from '@/types';
import InputField from './InputField';
import SelectField from './SelectField';
import { DatePicker } from './DatePicker';
import NoteTextarea from './NoteTextarea';
import { CheckboxGroup } from './CheckboxGroup';
import PhoneNumberInput from './PhoneNumberInput';
import InputFieldWithIcon from './InputFieldWithIcon';
import EmailInbox from '@/app/assets/svg/emailInbox';
import { Country, State, City } from 'country-state-city';
import { useSaveStudentMutation } from '@/lib/redux/api/studentApi';
import { transformFormDataToStudentData, validateStudentData } from '@/lib/utils/studentDataTransform';
import { useToast } from '@/hooks/use-toast';
import {
    maritalStatus,
    gender,
    socialPlatform,
    subjects,
    prefferedCountry,
    Reference
} from '@/common';
import FormSubSection from './FormSubSection';
import TextAreaField from './TextAreaField';
import SelectAndSearchCombobox from './SelectAndSearchCombobox';
import MultiSelectDropdown from './MultiSelectDropdown';
import { Switch } from '@/components/ui/switch';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import { setCurrentStudent } from '@/lib/redux/slices/studentSlice';

interface DropdownOption {
    label: string;
    value: string;
}

interface PersonalProps {
    studentId?: string | number;
    initialData?: any;
    onSave?: (data: any) => void;
}

export interface PersonalRef {
    saveForm: () => Promise<{ success: boolean; data?: any; error?: string }>;
    getFormData: () => FormData;
    setFormData: (data: Partial<FormData>) => void;
}

const Personal = React.forwardRef<PersonalRef, PersonalProps>(({ studentId, initialData, onSave }, ref) => {
    const countriesList = Country.getAllCountries();
    const [states, setStates] = useState(State.getStatesOfCountry(countriesList[0].isoCode));
    const studentPersonalInfo = useSelector((state: RootState) => state.student.currentStudent);
    console.log('studentPersonalInfo', studentPersonalInfo);

    // Relationship options for sponsor
    const relationshipOptions = [
        { value: 'father', label: 'Father' },
        { value: 'mother', label: 'Mother' },
        { value: 'spouse', label: 'Spouse' },
        { value: 'sibling', label: 'Sibling' },
        { value: 'uncle', label: 'Uncle' },
        { value: 'aunt', label: 'Aunt' },
        { value: 'grandparent', label: 'Grandparent' },
        { value: 'friend', label: 'Friend' },
        { value: 'employer', label: 'Employer' },
        { value: 'other', label: 'Other' }
    ];

    // Social links state
    const [socialLinks, setSocialLinks] = useState<Array<{platform: string, url: string}>>([]);
    const [currentPlatform, setCurrentPlatform] = useState('');
    const [currentUrl, setCurrentUrl] = useState('');

    // Address state variables
    const [sameAsPresent, setSameAsPresent] = useState(false);
    const [presentAddress, setPresentAddress] = useState({
        address: '',
        country: '',
        state: '',
        city: '',
        zip: '',
    });
    const [permanentAddress, setPermanentAddress] = useState({
        address: '',
        country: '',
        state: '',
        city: '',
        zip: '',
    });

    // State and city options for dropdowns
    const [presentStates, setPresentStates] = useState<DropdownOption[]>([]);
    const [presentCities, setPresentCities] = useState<DropdownOption[]>([]);
    const [permanentStates, setPermanentStates] = useState<DropdownOption[]>([]);
    const [permanentCities, setPermanentCities] = useState<DropdownOption[]>([]);

    // Redux hooks
    const [saveStudent] = useSaveStudentMutation();
    const { toast } = useToast();
    const {
        register,
        control,
        getValues,
        setValue,
        watch,
        handleSubmit,
        formState: { errors }
    } = useForm<FormData>({
        defaultValues: {
            studentId: studentId,
            present_country: countriesList[0].isoCode,
            present_state: states[0]?.isoCode,
            permanent_country: countriesList[0].isoCode,
            permanent_state: states[0]?.isoCode,
            prefferedCountry: [],
            preferred_subject: [],
            selectedPlatform: socialPlatform[0].value,
            ...initialData,
        },
    });
    const selectedPresentCountry = watch('present_country');
    const selectedPermanentCountry = watch('permanent_country');
    const maritalStatusValue = watch('marital_status');

    // Update states when country changes
    useEffect(() => {
        if (selectedPresentCountry) {
            const countryStates = State.getStatesOfCountry(selectedPresentCountry);
            setStates(countryStates);
            setValue('present_state', countryStates[0]?.isoCode || '');
        }
    }, [selectedPresentCountry, setValue]);

    useEffect(() => {
        if (selectedPermanentCountry) {
            const countryStates = State.getStatesOfCountry(selectedPermanentCountry);
            setValue('permanent_state', countryStates[0]?.isoCode || '');
        }
    }, [selectedPermanentCountry, setValue]);

    // Country options for dropdowns
    const countryOptions: DropdownOption[] = countriesList.map((country) => ({
        label: country.name,
        value: country.isoCode,
    }));

    // Address change handlers
    const handlePresentChange = (field: string, value: string) => {
        setPresentAddress((prev) => ({ ...prev, [field]: value }));
    };

    const handlePermanentChange = (field: string, value: string) => {
        setPermanentAddress((prev) => ({ ...prev, [field]: value }));
    };

    // Social links handler
    const handleAddSocialLink = () => {
        if (currentPlatform && currentUrl) {
            const newSocialLink = {
                platform: currentPlatform,
                url: currentUrl
            };
            setSocialLinks(prev => [...prev, newSocialLink]);
            setCurrentPlatform('');
            setCurrentUrl('');
        }
    };

    const handleRemoveSocialLink = (index: number) => {
        setSocialLinks(prev => prev.filter((_, i) => i !== index));
    };

    // Effect to sync permanent address with present address when toggle is on
    useEffect(() => {
        if (sameAsPresent) {
            setPermanentAddress({ ...presentAddress });
            setPermanentStates(presentStates);
            setPermanentCities(presentCities);
        }
    }, [sameAsPresent, presentAddress, presentStates, presentCities]);

    // Effects to handle country/state/city dependencies for present address
    useEffect(() => {
        if (presentAddress.country) {
            const states = State.getStatesOfCountry(presentAddress.country).map(state => ({
                label: state.name,
                value: state.isoCode,
            }));
            setPresentStates(states);
            setPresentCities([]);
            setPresentAddress(prev => ({ ...prev, state: '', city: '' }));
        }
    }, [presentAddress.country]);

    useEffect(() => {
        if (presentAddress.state) {
            const cities = City.getCitiesOfState(presentAddress.country, presentAddress.state).map(city => ({
                label: city.name,
                value: city.name,
            }));
            setPresentCities(cities);
            setPresentAddress(prev => ({ ...prev, city: '' }));
        }
    }, [presentAddress.state, presentAddress.country]);

    // Effects to handle country/state/city dependencies for permanent address
    useEffect(() => {
        if (!sameAsPresent && permanentAddress.country) {
            const states = State.getStatesOfCountry(permanentAddress.country).map(state => ({
                label: state.name,
                value: state.isoCode,
            }));
            setPermanentStates(states);
            setPermanentCities([]);
            setPermanentAddress(prev => ({ ...prev, state: '', city: '' }));
        }
    }, [permanentAddress.country, sameAsPresent]);

    useEffect(() => {
        if (!sameAsPresent && permanentAddress.state) {
            const cities = City.getCitiesOfState(permanentAddress.country, permanentAddress.state).map(city => ({
                label: city.name,
                value: city.name,
            }));
            setPermanentCities(cities);
            setPermanentAddress(prev => ({ ...prev, city: '' }));
        }
    }, [permanentAddress.state, permanentAddress.country, sameAsPresent]);

    const onSubmit: SubmitHandler<FormData> = async (data) => {
        try {
            const studentData = transformFormDataToStudentData(data);
            const validationErrors = validateStudentData(studentData);

            if (validationErrors.length > 0) {
                toast({
                    title: "Validation Error",
                    description: validationErrors.join(', '),
                    variant: "destructive",
                });
                return;
            }

            const result = await saveStudent(studentData).unwrap();            

            if (result.success) {
                toast({
                    title: "Success",
                    description: "Student information saved successfully!",
                });
                console.log('data saved');
                
                console.log('Student set to Redux:', result?.data);
                if (onSave) {
                    onSave(result.data);
                }
            }
        } catch (error: any) {
            console.error('Error saving student:', error);
            toast({
                title: "Error",
                description: error?.data?.message || "Failed to save student information",
                variant: "destructive",
            });
        }
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        saveForm: async () => {
            try {
                const formData = getValues();
                // Include social links and address data in the form data
                const formDataWithExtras = {
                    ...formData,
                    socialLinks: socialLinks,
                    presentAddress: presentAddress,
                    permanentAddress: permanentAddress
                };
                const studentData = transformFormDataToStudentData(formDataWithExtras);
                const validationErrors = validateStudentData(studentData);

                if (validationErrors.length > 0) {
                    return { success: false, error: validationErrors.join(', ') };
                }

                const result = await saveStudent(studentData).unwrap();
                return { success: result.success, data: result.data };
            } catch (error: any) {
                return { success: false, error: error?.data?.message || "Failed to save" };
            }
        },
        getFormData: () => {
            const formData = getValues();
            return {
                ...formData,
                socialLinks: socialLinks,
                presentAddress: presentAddress,
                permanentAddress: permanentAddress
            };
        },
        setFormData: (data: Partial<FormData>) => {
            Object.keys(data).forEach(key => {
                setValue(key as keyof FormData, data[key as keyof FormData]);
            });
            // Set social links if provided
            if ((data as any).socialLinks) {
                setSocialLinks((data as any).socialLinks);
            }
        }
    }));



    return (
        <div className="w-full max-w-6xl mx-auto bg-white p-6">
            {/* Header */}
            <div className='border-b border-gray-200 pb-4 mb-8'>
                <h2 className='text-xl font-semibold text-gray-900'>Info Form</h2>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
                {/* Personal Details Section */}
                <div className='space-y-6'>
                    <h3 className='text-base font-medium text-blue-600 border-b border-blue-100 pb-2'>
                        Personal Details
                    </h3>
                    
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                        <div>
                            <InputField 
                                type='text' 
                                id='last_name'
                                placeholder='Last Name' 
                                label='Last Name / Surname' 
                                register={register('last_name', { required: 'Last name is required' })}
                                errorMessage={errors.last_name?.message}
                            />
                        </div>
                        <div>
                            <InputField 
                                id='first_name'
                                placeholder='First Name' 
                                type='text' 
                                register={register('first_name', { required: 'First name is required' })}
                                label='First Name / Given Name' 
                            />
                        </div>
                        <div>
                            <InputField 
                                id='language'
                                type='text' 
                                label='Name in Native Language' 
                                register={register('language')}
                            />
                        </div>
                        <div>
                            <InputFieldWithIcon 
                                label='Email' 
                                placeholder='Email'
                                icon={<EmailInbox />}
                                type='email'
                                register={register('email', { required: 'Email is required' })}
                            />
                        </div>
                        <div>
                            <Controller
                                name='dob'
                                control={control}
                                render={({ field }) => (
                                    <DatePicker 
                                        value={field.value}
                                        onChange={field.onChange}  
                                    />
                                )}
                            />
                        </div>
                        <div>
                            <Controller
                                name='gender'
                                control={control}
                                render={({ field }) => (
                                <CheckboxGroup
                                    options={gender}
                                    value={field.value}
                                    onChange={field.onChange}
                                />
                                )}
                            />
                        </div>
                        <div>
                            <Controller
                                name='phoneNumber'
                                control={control}
                                render={({ field }) => (
                                    <PhoneNumberInput
                                        label='Phone Number'
                                        value={field.value}
                                        onChange={field.onChange}
                                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                                    />
                                )}
                            />
                        </div>
                        <div>
                            <Controller
                                name='gurdian_phone_number'
                                control={control}
                                render={({ field }) => (
                                    <PhoneNumberInput
                                        label='Guardian Phone Number'
                                        value={field.value}
                                        onChange={field.onChange}
                                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                                    />
                                )}
                            />
                        </div>
                        <div>
                            <InputField
                                id='fathers_name'
                                placeholder="Father's Name"
                                type='text'
                                register={register('fathers_name')}
                                label="Father's Name"
                            />
                        </div>
                        <div>
                            <InputField
                                id='mothers_name'
                                register={register('mothers_name')}
                                placeholder="Mother's Name"
                                type='text'
                                label="Mother's Name"
                            />
                        </div>
                        <div>
                            <InputField 
                                id='national_id'
                                label='National ID (NID)'
                                placeholder='Enter your national id number' 
                                type='text' 
                                register={register('national_id')}
                            />
                        </div>
                        <div>
                            <InputField 
                                id='passport_number'
                                placeholder='Enter your passport number' 
                                type='text' 
                                register={register('passport_number')}
                                label='Passport Number' 
                            />
                        </div>
                    </div>
                </div>

                <FormSubSection heading={'Address'} >
                        <div className='grid grid-cols-2 gap-x-6 '>
                            <div className='grid gird-col-1 gap-y-[30px] border border-tertiary/20 rounded-[10px] p-5'>
                                <TextAreaField
                                    label='Present Address'
                                    placeholder='Enter Address'
                                    rows={0}
                                    className='h-fit'
                                    value={presentAddress.address}
                                    onChange={e => handlePresentChange('address', e.target.value)}
                                />
                                <div className='grid grid-cols-2 gap-x-6 gap-y-[30px]'>
                                    <SelectAndSearchCombobox
                                        label='Country'
                                        options={countryOptions}
                                        className='py-3'
                                        buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={presentAddress.country}
                                        onChange={val => handlePresentChange('country', val)}
                                    />
                                    <SelectAndSearchCombobox
                                        label='State'
                                        options={presentStates}
                                        className='py-3'
                                        buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={presentAddress.state}
                                        onChange={val => handlePresentChange('state', val)}
                                        disabled={!presentAddress.country}
                                    />
                                    <SelectAndSearchCombobox
                                        label='City'
                                        options={presentCities}
                                        className='py-3'
                                        buttonClassName=' border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={presentAddress.city}
                                        onChange={val => handlePresentChange('city', val)}
                                        disabled={!presentAddress.state}
                                    />
                                    <InputField
                                        id='zip_code'
                                        type='text'
                                        label='Postal/Zip Code'
                                        className='py-3'
                                        value={presentAddress.zip}
                                        onChange={e => handlePresentChange('zip', e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className='grid gird-col-1 gap-y-[30px] bg-primaryOne rounded-[10px] p-5'>
                                <div className='relative'>
                                    <div className='flex items-center gap-2 absolute top-0 right-0'>
                                        <label htmlFor='same_as_present' className='text-xs text-primaryColor cursor-pointer'>*Same as Present Address</label>
                                        <Switch
                                            id='same_as_present'
                                            className='border border-grayTwo h-4 w-7'
                                            classes='bg-grayTwo h-3 w-3 data-[state=checked]:translate-x-3'
                                            checked={sameAsPresent}
                                            onCheckedChange={setSameAsPresent}
                                        />
                                    </div>
                                    <TextAreaField
                                        label='Permanent Address'
                                        placeholder='Enter Address'
                                        rows={0}
                                        className='h-fit'
                                        value={permanentAddress.address}
                                        onChange={e => handlePermanentChange('address', e.target.value)}
                                        disabled={sameAsPresent}
                                    />
                                </div>
                                <div className='grid grid-cols-2 gap-x-6 gap-y-[30px]'>
                                    <SelectAndSearchCombobox
                                        label='Country'
                                        options={countryOptions}
                                        className='py-3'
                                        buttonClassName=' bg-primaryOne border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={permanentAddress.country}
                                        onChange={val => handlePermanentChange('country', val)}
                                        disabled={sameAsPresent}
                                    />
                                    <SelectAndSearchCombobox
                                        label='State'
                                        options={permanentStates}
                                        className='py-3'
                                        buttonClassName=' bg-primaryOne border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={permanentAddress.state}
                                        onChange={val => handlePermanentChange('state', val)}
                                        disabled={sameAsPresent || !permanentAddress.country}
                                    />
                                    <SelectAndSearchCombobox
                                        label='City'
                                        options={permanentCities}
                                        className='py-3'
                                        buttonClassName=' bg-primaryOne border-opacity-20 focus-within:outline-none !py-3 px-3.5 rounded-lg w-full placeholder:text-grayThree'
                                        selectedValue={permanentAddress.city}
                                        onChange={val => handlePermanentChange('city', val)}
                                        disabled={sameAsPresent || !permanentAddress.state}
                                    />
                                    <InputField
                                        id='zip_code'
                                        type='text'
                                        label='Postal/Zip Code'
                                        className='py-3 bg-primaryOne'
                                        value={permanentAddress.zip}
                                        onChange={e => handlePermanentChange('zip', e.target.value)}
                                        disabled={sameAsPresent}
                                    />
                                </div>
                            </div>
                        </div>
                    </FormSubSection>

                {/* Marital Details Section */}
                <div className='space-y-6'>
                    <h3 className='text-base font-medium text-blue-600 border-b border-blue-100 pb-2'>
                        Marital Details
                    </h3>

                    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                        <div>
                            <Controller
                                name='marital_status'
                                control={control}
                                render={({ field }) => (
                                    <SelectField
                                        label='Marital Status'
                                        options={maritalStatus}
                                        value={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                        </div>
                        {maritalStatusValue === 'married' && (
                            <>
                                <div>
                                    <InputField
                                        id='spouse_name'
                                        type='text'
                                        label='Spouse Name'
                                        register={register('spouse_name')}
                                    />
                                </div>
                                <div>
                                    <Controller
                                        name='phoneNumber'
                                        control={control}
                                        render={({ field }) => (
                                            <PhoneNumberInput
                                                label='Phone Number'
                                                value={field.value}
                                                onChange={field.onChange}
                                                className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                                            />
                                        )}
                                    />
                                </div>
                                <div>
                                    <InputField
                                        id='spouse_passport_number'
                                        type='text'
                                        label='Spouse Passport Number'
                                        register={register('spouse_passport_number')}
                                    />
                                </div>
                            </>
                        )}
                    </div>
                </div>

                {/* Sponsor Details Section */}
                <FormSubSection heading={'Sponsor Details'}>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                        <div>
                            <InputField
                                id='sponsor_name'
                                type='text'
                                label='Sponsor Name'
                                placeholder='Enter Name'
                                register={register('sponsor_name')}
                            />
                        </div>
                        <div>
                            <Controller
                                name='sponsor_phone'
                                control={control}
                                render={({ field }) => (
                                    <PhoneNumberInput
                                        label='Phone Number'
                                        value={field.value as string}
                                        onChange={field.onChange}
                                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                                    />
                                )}
                            />
                        </div>
                        <div>
                            <InputField
                                id='sponsor_national_id'
                                type='text'
                                label='National Identity Card/NID Card/Aadhaar Card'
                                placeholder='Enter ID Number'
                                register={register('sponsor_national_id')}
                            />
                        </div>
                        <div>
                            <Controller
                                name='relationship'
                                control={control}
                                render={({ field }) => (
                                    <SelectField
                                        label='Relationship'
                                        placeholder='Relationship with Sponsor'
                                        options={relationshipOptions}
                                        value={field.value as string}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                        </div>
                    </div>
                </FormSubSection>

                {/* Emergency Contact Section */}
                <FormSubSection heading={'Emergency Contact'}>
                    <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                        <div>
                            <InputField
                                id='emergency_last_name'
                                type='text'
                                label='Last Name / Surname'
                                placeholder='Last Name'
                                register={register('emergency_last_name')}
                            />
                        </div>
                        <div>
                            <InputField
                                id='emergency_middle_name'
                                type='text'
                                label='Middle Name'
                                placeholder='Middle Name'
                                register={register('emergency_middle_name')}
                            />
                        </div>
                        <div>
                            <InputField
                                id='emergency_first_name'
                                type='text'
                                label='First Name / Given Name'
                                placeholder='First Name'
                                register={register('emergency_first_name')}
                            />
                        </div>
                        <div>
                            <Controller
                                name='emergency_phone_home'
                                control={control}
                                render={({ field }) => (
                                    <PhoneNumberInput
                                        label='Phone Number (Home)'
                                        value={field.value as string}
                                        onChange={field.onChange}
                                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                                    />
                                )}
                            />
                        </div>
                        <div>
                            <Controller
                                name='emergency_phone_mobile'
                                control={control}
                                render={({ field }) => (
                                    <PhoneNumberInput
                                        label='Phone Number (Mobile)'
                                        value={field.value as string}
                                        onChange={field.onChange}
                                        className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-3 px-3.5 mt-0.5 rounded-lg w-full"
                                    />
                                )}
                            />
                        </div>
                        <div>
                            <Controller
                                name='emergency_relation'
                                control={control}
                                render={({ field }) => (
                                    <SelectField
                                        label='Relationship'
                                        placeholder='Relationship with Applicant'
                                        options={relationshipOptions}
                                        value={field.value as string}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                        </div>
                    </div>
                </FormSubSection>

                {/* Preferred Subject and Country Section */}
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                        <Controller
                            name='preferred_subject'
                            control={control}
                            render={({ field }) => {
                                const selectedOptions = subjects.filter(subject =>
                                    (field.value || []).includes(subject.value)
                                );

                                return (
                                    <MultiSelectDropdown
                                        label='Preferred Subject'
                                        data={subjects}
                                        value={selectedOptions}
                                        onChange={(selected) => {
                                            const values = selected.map(option => option.value);
                                            field.onChange(values);
                                        }}
                                    />
                                );
                            }}
                        />
                    </div>
                    <div>
                        <Controller
                            name='prefferedCountry'
                            control={control}
                            render={({ field }) => {
                                const selectedOptions = prefferedCountry.filter(country =>
                                    (field.value || []).includes(country.value)
                                );

                                return (
                                    <MultiSelectDropdown
                                        label='Preferred Country'
                                        data={prefferedCountry}
                                        value={selectedOptions}
                                        onChange={(selected) => {
                                            const values = selected.map(option => option.value);
                                            field.onChange(values);
                                        }}
                                    />
                                );
                            }}
                        />
                    </div>
                </div>

                {/* Social Links and Reference Section */}
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                        <label className='block text-sm font-medium text-grayFive mb-2'>Social Links</label>
                        <div className='grid grid-cols-2 gap-3 mb-4'>
                            <SelectField
                                placeholder='Platform'
                                options={socialPlatform}
                                value={currentPlatform}
                                onChange={setCurrentPlatform}
                            />
                            <div className="flex w-full text-sm border border-tertiary border-opacity-20 rounded-lg">
                                <input
                                    id="social_link_url"
                                    type="text"
                                    placeholder="profile link paste here..."
                                    value={currentUrl}
                                    onChange={(e) => setCurrentUrl(e.target.value)}
                                    className="w-full pl-5 rounded-lg focus:outline-none"
                                />
                                <button
                                    type="button"
                                    onClick={handleAddSocialLink}
                                    className="px-5 py-3 bg-primaryThree text-primaryColor text-sm rounded-md hover:bg-blue-700 hover:text-white"
                                >
                                    Add
                                </button>
                            </div>
                        </div>
                        <div className='flex flex-wrap gap-2'>
                            {socialLinks.map((link, index) => (
                                <span
                                    key={index}
                                    className='inline-flex items-center px-3 py-1 rounded-full text-xs bg-gray-100 text-gray-800 border'
                                >
                                    {link.platform}
                                    <button
                                        type="button"
                                        onClick={() => handleRemoveSocialLink(index)}
                                        className="ml-1 text-gray-500 hover:text-gray-700"
                                    >
                                        ×
                                    </button>
                                </span>
                            ))}
                        </div>
                    </div>
                    <div>
                        <Controller
                            name='reference'
                            control={control}
                            render={({ field }) => (
                                <SelectField
                                    label='Reference'
                                    placeholder='Select Reference'
                                    options={Reference}
                                    value={Array.isArray(field.value) ? field.value[0] || '' : field.value || ''}
                                    onChange={field.onChange}
                                />
                            )}
                        />
                    </div>
                </div>

                {/* Note Section */}
                <div className='space-y-6'>
                    <div className='col-span-1 md:col-span-2'>
                        <NoteTextarea
                            register={register('note')}
                            rows={6}
                            className='h-[120px]'
                        />
                    </div>
                </div>
            </form>
        </div>
    );
});

Personal.displayName = 'Personal';

export default Personal;
