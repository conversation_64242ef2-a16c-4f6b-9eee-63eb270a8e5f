'use client'

import { tabs } from '@/common';
import Payment from './Payment';
import Personal from './Personal';
import Documents from './Documents';
import Education from './Education';
import Application from './Application';
import Save from '@/app/assets/svg/save';
import { useRouter } from 'next/navigation';
import ButtonWithIcon from './ButtonWithIcon';
import { useSearchParams } from 'next/navigation';
import ArrowLeft from '@/app/assets/svg/arrow-left';
import ArrowForward from '@/app/assets/svg/arrow_forward';
import React, { Suspense, useEffect, useState, useRef} from 'react';
import { PersonalRef } from './Personal';
import { EducationRef } from './Education';
import { DocumentsRef, ApplicationRef } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useDispatch } from 'react-redux';
import { setCurrentStudent } from '@/lib/redux/slices/studentSlice';

interface TabProps {
    setSelectedTab: (tabId: number) => void;
};

const TabContent = ({setSelectedTab}: TabProps) => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const tabParam = searchParams.get('tab');
    const { toast } = useToast();
    const dispatch = useDispatch();

    const [activeTab, setActiveTab] = useState<number>(() => {
        if (tabParam === 'application') return 3;
        return 1;
    });

    // Refs for each component
    const personalRef = useRef<PersonalRef>(null);
    const educationRef = useRef<EducationRef>(null);
    const documentsRef = useRef<DocumentsRef>(null);
    const applicationRef = useRef<ApplicationRef>(null);

    useEffect(() => {
        const tabName = tabs.find((tab) => tab.id === activeTab)?.label.toLowerCase();
        if (tabName) {
            router.replace(`?tab=${tabName}`, { scroll: false });
        }
    }, [activeTab, router]);

    // Save handler
    const handleSave = async () => {
        try {
            switch (activeTab) {
                case 1: // Personal tab
                    if (personalRef.current) {
                        const result = await personalRef.current.saveForm();
                        if (result.success) {
                            toast({
                                title: "Success",
                                description: "Personal information saved successfully!",
                            });
                            dispatch(setCurrentStudent(result.data));
                        } else {
                            toast({
                                title: "Validation Error",
                                description: result.error || "Please fill in all required fields",
                                variant: "destructive",
                            });
                        }
                    }
                    break;
                case 2: // Education tab
                    if (educationRef.current) {
                        const result = await educationRef.current.saveForm();
                        if (result.success) {
                            toast({
                                title: "Success",
                                description: "Education information saved successfully!",
                            });
                        } else {
                            toast({
                                title: "Validation Error",
                                description: result.error || "Please fill in all required fields",
                                variant: "destructive",
                            });
                        }
                    }
                    break;
                case 3: // Application tab
                    if (applicationRef.current) {
                        const result = await applicationRef.current.saveForm();
                        if (result.success) {
                            toast({
                                title: "Success",
                                description: "Applications saved successfully!",
                            });
                        } else {
                            toast({
                                title: "Validation Error",
                                description: result.error || "Please fill in all required fields",
                                variant: "destructive",
                            });
                        }
                    }
                    break;
                case 5: // Documents tab
                    if (documentsRef.current) {
                        const result = await documentsRef.current.saveForm();
                        if (result.success) {
                            toast({
                                title: "Success",
                                description: "Documents saved successfully!",
                            });
                        } else {
                            toast({
                                title: "Validation Error",
                                description: result.error || "Please fill in all required fields",
                                variant: "destructive",
                            });
                        }
                    }
                    break;
                // Add other cases for other tabs when needed
                default:
                    toast({
                        title: "Info",
                        description: "Save functionality not implemented for this tab",
                    });
            }
        } catch (error) {
            toast({
                title: "Error",
                description: "An unexpected error occurred while saving",
                variant: "destructive",
            });
        }
    };

    const renderContent = () => {
        switch (activeTab) {
            case 1:
                return <Personal ref={personalRef} />;
            case 2:
                return <Education ref={educationRef} studentId="1" />;
            case 3:
                return <Application ref={applicationRef} studentId="1" />;
            case 4:
                return <Payment />;
            case 5:
                return <Documents ref={documentsRef} studentId="1" />;
            default:
                return null;
        }
    };

    return (
        <>
            <div className='w-full pb-20'>
                <div className='scrollbar-hide px-4 antialiased md:px-0 flex overflow-x-auto whitespace-nowrap justify-start md:justify-center rounded-[14px] bg-white drop-shadow-7xl space-x-14'>
                    {tabs.map((tab) => (
                        <button
                            key={tab.id}
                            className={`py-4 font-semibold text-base leading-[20px] ${
                              activeTab === tab.id
                                ? 'border-b-2 border-primaryColor text-primaryColor'
                                : 'text-grayFive hover:text-primaryColor'
                            }`}
                            onClick={() => {
                                setActiveTab(tab.id)
                                setSelectedTab(tab.id);
                            }}
                        >
                            {tab.label}
                        </button>
                    ))}
                </div>
                <div>{renderContent()}</div>
                <div className='flex gap-7 justify-end pt-12'>
                    <ButtonWithIcon
                        label='Back'
                        icon={<ArrowLeft />}
                        className='text-tertiary'
                    />
                    <ButtonWithIcon
                        label='Save'
                        icon={<Save />}
                        className='border border-tertiary text-tertiary rounded-[20px]'
                        onClick={handleSave}
                    />
                    <ButtonWithIcon
                        label='Next'
                        icon={<ArrowForward className='text-white w-4 h-4' />}
                        className='bg-tertiary flex-row-reverse text-primaryOne border border-tertiary rounded-[220px]'
                    />
                </div>
            </div>
        </>
    );
};

const Tab = ({setSelectedTab}: TabProps) => (
    <Suspense fallback={<div>Loading...</div>}>
        <TabContent setSelectedTab={setSelectedTab}/>
    </Suspense>
);

export default Tab;