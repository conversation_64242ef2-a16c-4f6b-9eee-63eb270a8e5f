'use client';

import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const StudentProfile = () => {
  const currentStudent = useSelector((state: RootState) => state.student.currentStudent?.student?.personalInfo);
  console.log('current student', currentStudent);
  

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const InfoRow = ({ label, value }: { label: string; value: string }) => (
    <div className="grid grid-cols-[220px_1fr] text-sm">
      <p className="text-gray-500 font-medium">{label} :</p>
      <p className="text-gray-900 font-normal">{value || 'N/A'}</p>
    </div>
  );

  const getSocialIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
        case 'facebook':
        return '📘';
        case 'linkedin':
        return '🔗';
        case 'twitter':
        return '🐦';
        case 'instagram':
        return '📸';
        case 'youtube':
        return '▶️';
        case 'github':
        return '🐱';
        case 'x':
        return '❌';
        default:
        return '🌐';
    }
  };


  return (
    <div className="bg-white rounded-xl px-6 py-8 shadow-sm space-y-8">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <Avatar className="h-14 w-14">
            <AvatarImage src="https://github.com/shadcn.png" alt="Profile" />
            <AvatarFallback>{getInitials(`${currentStudent?.firstName} ${currentStudent?.lastName}`)}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{currentStudent?.firstName} {currentStudent?.lastName}</h2>
            <p className="text-sm text-gray-500">ID: #{currentStudent?.studentId}</p>
          </div>
        </div>
        <div className="text-sm space-y-1 text-right">
          <p><span className="text-gray-500 font-medium">Passport:</span> {currentStudent?.passport || 'N/A'}</p>
          <p><span className="text-gray-500 font-medium">National ID:</span> {currentStudent?.nid || 'N/A'}</p>
        </div>
      </div>

      {/* Personal Info */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-4">Personal Information</h3>
        <div className="grid md:grid-cols-2 gap-y-4 gap-x-8 bg-gray-50 p-5 rounded-md">
          <InfoRow label="First Name / Given Name" value={currentStudent?.firstName || ''} />
          <InfoRow label="Last Name / Surname" value={currentStudent?.lastName || ''} />
          <InfoRow label="Date Of Birth" value={currentStudent?.dateOfBirth || ''} />
          <InfoRow label="NID" value={currentStudent?.nid || ''} />
          <InfoRow label="Gender" value={currentStudent?.gender || ''} />
          <InfoRow label="Email" value={currentStudent?.email || ''} />
          <InfoRow label="Name In Native Language" value={currentStudent?.nameInNative || ''} />
          <InfoRow label="Phone Number" value={currentStudent?.phone || ''} />
          <InfoRow label="Present Address" value={currentStudent?.presentAddress?.address || ''} />
          <InfoRow label="Permanent Address" value={currentStudent?.permanentAddress?.address || ''} />
          <InfoRow label="Father Name" value={currentStudent?.fatherName || ''} />
          <InfoRow label="Mother Name" value={currentStudent?.motherName || ''} />
          <InfoRow label="Guardian Phone" value={currentStudent?.guardianPhone || ''} />
          <InfoRow label="Marital Status" value={currentStudent?.maritalStatus?.status || ''} />
          <InfoRow label="Spouse Name" value={currentStudent?.maritalStatus?.spouseName || ''} />
          <InfoRow label="Spouse Passport Number" value={currentStudent?.maritalStatus?.spousePassport || ''} />
          <InfoRow label="Spouse Phone Number" value={currentStudent?.maritalStatus?.spousePhone || ''} />
          <InfoRow label="Sponsor Name" value={currentStudent?.sponsor?.name || ''} />
          <InfoRow label="Relationship with Sponsor" value={currentStudent?.sponsor?.relation || ''} />
          <InfoRow label="Sponsor Phone Number" value={currentStudent?.sponsor?.phone || ''} />
          <InfoRow label="Sponsor NID" value={currentStudent?.sponsor?.email || ''} />
        </div>
      </div>

      {/* Emergency Contact */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-4">Emergency Contact</h3>
        <div className="grid md:grid-cols-2 gap-y-4 gap-x-8 bg-gray-50 p-5 rounded-md">
          <InfoRow label="First Name / Given Name" value={currentStudent?.emergencyContact?.firstName || ''} />
          <InfoRow label="Last Name / Surname" value={currentStudent?.emergencyContact?.lastName || ''} />
          <InfoRow label="Middle Name" value={currentStudent?.emergencyContact?.middleName || ''} />
          <InfoRow label="Relationship" value={currentStudent?.emergencyContact?.relation || ''} />
          <InfoRow label="Phone Number (Home)" value={currentStudent?.emergencyContact?.phoneHome || ''} />
          <InfoRow label="Phone Number (Mobile)" value={currentStudent?.emergencyContact?.phoneMobile || ''} />
        </div>
      </div>

    {/* Social Links */}
    <div>
        <h3 className="text-base font-semibold text-gray-900 mb-4 flex items-center justify-between">
          Social Links
          <button className="text-xs text-primaryColor border border-primaryColor px-2 py-1 rounded-full">+ Add social link</button>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {(currentStudent?.socialLinks || []).map((link, index) => (
            <div key={index} className="flex items-center justify-between border rounded-lg px-4 py-2">
              <div className="flex items-center gap-2">
                <span className="text-xl">{getSocialIcon(link.platform)}</span>
                <span className="text-sm text-gray-700">@{link.url}</span>
              </div>
              <a
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primaryColor"
              >↗</a>
            </div>
          ))}
        </div>
      </div>

      {/* Notes */}
      <div>
        <h3 className="text-base font-semibold text-gray-900 mb-2">Notes</h3>
        <div className="bg-gray-50 p-4 rounded-md text-sm text-gray-700">
          {currentStudent?.note || 'No additional notes provided.'}
        </div>
      </div>
    </div>
  );
};

export default StudentProfile;