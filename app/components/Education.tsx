'use client';

import React, { useImperativeHandle, useState } from 'react';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { EducationFormData } from '@/types';
import InputField from './InputField';
import { DatePicker } from './DatePicker';
import NoteTextarea from './NoteTextarea';
import { Switch } from '@/components/ui/switch';
import SectionLayout from './layout/SectionLayout';
import AddNewFieldButton from './AddNewFieldButton';
import SelectAndSearchCombobox from './SelectAndSearchCombobox';
import { useSaveEducationMutation } from '@/lib/redux/api/studentApi';
import { useToast } from '@/hooks/use-toast';
import { Country } from 'country-state-city';
import Image from 'next/image';
import Error from '@/app/assets/svg/error.svg';
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';

// Education component interfaces
interface EducationProps {
    studentId?: string | number;
    initialData?: any;
    onSave?: (data: any) => void;
}

export interface EducationRef {
    saveForm: () => Promise<{ success: boolean; data?: any; error?: string }>;
    getFormData: () => EducationFormData;
    setFormData: (data: Partial<EducationFormData>) => void;
}
const Education = React.forwardRef<EducationRef, EducationProps>(({ studentId, initialData, onSave }, ref) => {
    const [saveEducation] = useSaveEducationMutation();
    const { toast } = useToast();

    // State for foreign degree switch
    const [hasForeignDegree, setHasForeignDegree] = useState(false);
    const [selectedCountry, setSelectedCountry] = useState('');

    // Country options
    const countriesList = Country.getAllCountries();
    const countryOptions = countriesList.map((country) => ({
        label: country.name,
        value: country.isoCode,
    }));

    // Exam options
    const examOptions = [
        { value: 'SSC', label: 'SSC' },
        { value: 'HSC', label: 'HSC' },
        { value: 'Bachelor', label: 'Bachelor' },
        { value: 'Master', label: 'Master' },
        { value: 'PhD', label: 'PhD' },
    ];

    const proficiencyExamOptions = [
        { value: 'IELTS', label: 'IELTS' },
        { value: 'TOEFL', label: 'TOEFL' },
        { value: 'PTE', label: 'PTE' },
        { value: 'Duolingo', label: 'Duolingo' },
    ];

    const gradeOptions = [
        { value: 'A+', label: 'A+' },
        { value: 'A', label: 'A' },
        { value: 'A-', label: 'A-' },
        { value: 'B+', label: 'B+' },
        { value: 'B', label: 'B' },
        { value: 'C+', label: 'C+' },
        { value: 'C', label: 'C' },
        { value: 'D', label: 'D' },
        { value: 'F', label: 'F' },
    ];

    const subjectOptions = [
        { value: 'Science', label: 'Science' },
        { value: 'Commerce', label: 'Commerce' },
        { value: 'Arts', label: 'Arts' },
        { value: 'Engineering', label: 'Engineering' },
        { value: 'Medicine', label: 'Medicine' },
        { value: 'Business', label: 'Business' },
    ];

    // Form setup
    const {
        register,
        control,
        handleSubmit,
        getValues,
        setValue
    } = useForm<EducationFormData>({
        defaultValues: {
            academic: [{
                foreignDegree: false,
                nameOfExam: '',
                institute: '',
                subject: '',
                board: '',
                grade: '',
                passingYear: ''
            }],
            proficiency: [{
                nameOfExam: '',
                overall: 0,
                R: 0,
                L: 0,
                W: 0,
                S: 0,
                examDate: '',
                expiryDate: '',
                note: ''
            }],
            publications: [{
                subject: '',
                journal: '',
                publicationDate: '',
                link: ''
            }],
            otherActivities: [{
                subject: '',
                certificationLink: '',
                startDate: '',
                endDate: ''
            }],
            ...initialData,
        },
    });

    // Field arrays for dynamic fields
    const { fields: academicFields, append: appendAcademic, remove: removeAcademic } = useFieldArray({
        control,
        name: 'academic'
    });

    const { fields: proficiencyFields, append: appendProficiency, remove: removeProficiency } = useFieldArray({
        control,
        name: 'proficiency'
    });

    const { fields: publicationFields, append: appendPublication, remove: removePublication } = useFieldArray({
        control,
        name: 'publications'
    });

    const { fields: activityFields, append: appendActivity, remove: removeActivity } = useFieldArray({
        control,
        name: 'otherActivities'
    });

    // Helper function to format dates
    const formatDate = (date: Date | string): string => {
        if (!date) return '';
        const d = new Date(date);
        return d.toISOString().split('T')[0]; // YYYY-MM-DD format
    };

    // Transform form data to API format
    const transformToApiFormat = (formData: EducationFormData) => {
        return {
            academic: formData.academic.map(item => ({
                ...item,
                foreignDegree: hasForeignDegree
            })),
            proficiency: formData.proficiency.map(item => ({
                nameOfExam: item.nameOfExam,
                score: {
                    overall: Number(item.overall) || 0,
                    R: Number(item.R) || 0,
                    L: Number(item.L) || 0,
                    W: Number(item.W) || 0,
                    S: Number(item.S) || 0,
                },
                examDate: formatDate(item.examDate),
                expiryDate: formatDate(item.expiryDate),
                note: item.note || ''
            })),
            publications: formData.publications.map(item => ({
                ...item,
                publicationDate: formatDate(item.publicationDate)
            })),
            otherActivities: formData.otherActivities.map(item => ({
                ...item,
                startDate: formatDate(item.startDate),
                endDate: formatDate(item.endDate)
            }))
        };
    };

    // Form submission handler
    const onSubmit = async (data: EducationFormData) => {
        try {
            if (!studentId) {
                toast({
                    title: "Error",
                    description: "Student ID is required",
                    variant: "destructive",
                });
                return;
            }

            const apiData = transformToApiFormat(data);
            const result = await saveEducation({
                studentId: studentId,
                educationData: apiData
            }).unwrap();

            if (result.success) {
                toast({
                    title: "Success",
                    description: "Education information saved successfully!",
                });
                if (onSave) {
                    onSave(result.data);
                }
            }
        } catch (error: any) {
            console.error('Error saving education:', error);
            toast({
                title: "Error",
                description: error?.data?.message || "Failed to save education information",
                variant: "destructive",
            });
        }
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        saveForm: async () => {
            try {
                const formData = getValues();
                const apiData = transformToApiFormat(formData);

                if (!studentId) {
                    return { success: false, error: "Student ID is required" };
                }

                const result = await saveEducation({
                    studentId: studentId,
                    educationData: apiData
                }).unwrap();

                return { success: result.success, data: result.data };
            } catch (error: any) {
                return { success: false, error: error?.data?.message || "Failed to save education" };
            }
        },
        getFormData: () => getValues(),
        setFormData: (data: Partial<EducationFormData>) => {
            Object.keys(data).forEach(key => {
                const value = data[key as keyof EducationFormData];
                if (value !== undefined) {
                    setValue(key as keyof EducationFormData, value as any);
                }
            });
        }
    }));

    // Add new field handlers
    const addAcademicField = () => {
        appendAcademic({
            foreignDegree: false,
            nameOfExam: '',
            institute: '',
            subject: '',
            board: '',
            grade: '',
            passingYear: ''
        });
    };

    const addProficiencyField = () => {
        appendProficiency({
            nameOfExam: '',
            overall: 0,
            R: 0,
            L: 0,
            W: 0,
            S: 0,
            examDate: '',
            expiryDate: '',
            note: ''
        });
    };

    const addPublicationField = () => {
        appendPublication({
            subject: '',
            journal: '',
            publicationDate: '',
            link: ''
        });
    };

    const addActivityField = () => {
        appendActivity({
            subject: '',
            certificationLink: '',
            startDate: '',
            endDate: ''
        });
    };
    return (
        <SectionLayout heading='Educational Form'>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
                {/* Academic Section */}
                <div>
                    <div className='flex flex-col md:flex-row gap-3 items-start md:items-center mb-6'>
                        <h3 className='font-semibold text-xl leading-6 tracking-[0.4px] text-graySix'>
                            Academic
                        </h3>
                        <div className='flex gap-3 items-center'>
                            <Switch
                                checked={hasForeignDegree}
                                onCheckedChange={setHasForeignDegree}
                                id='foreign-degree-switch'
                            />
                            <p className='font-normal text-xs md:text-sm leading-5 text-[#4B5563] text-opacity-70'>
                                If You Have Foreign Degree, Please Active The Button
                            </p>
                            <HoverCard>
                                <HoverCardTrigger>
                                    <Image width={16} height={16} src={Error} alt='Error icon' />
                                </HoverCardTrigger>
                                <HoverCardContent className='w-[290px] font-medium text-xs text-white -mt-11 ml-6 rounded-lg bg-grayFour' align='start'>
                                    If you have a foreign degree, please specify the country where
                                    you earned your degree in the dropdown below.
                                </HoverCardContent>
                            </HoverCard>
                        </div>
                    </div>

                    {hasForeignDegree && (
                        <div className='grid grid-cols-1 md:grid-cols-2 mb-6 gap-6'>
                            <SelectAndSearchCombobox
                                options={countryOptions}
                                label='Country'
                                placeholder='Select Country'
                                selectedValue={selectedCountry}
                                onChange={setSelectedCountry}
                            />
                        </div>
                    )}

                    {academicFields.map((field, index) => (
                        <div key={field.id} className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-[30px]'>
                            {academicFields.length > 1 && (
                                <div className='md:col-span-2 flex justify-end'>
                                    <button
                                        type="button"
                                        onClick={() => removeAcademic(index)}
                                        className="text-red-500 hover:text-red-700 text-sm"
                                    >
                                        Remove
                                    </button>
                                </div>
                            )}

                            <Controller
                                name={`academic.${index}.nameOfExam`}
                                control={control}
                                render={({ field }) => (
                                    <SelectAndSearchCombobox
                                        options={examOptions}
                                        label='Name of Exam'
                                        selectedValue={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />

                            <InputField
                                id={`academic-institute-${index}`}
                                type='text'
                                label='Institute'
                                register={register(`academic.${index}.institute`)}
                            />

                            <Controller
                                name={`academic.${index}.subject`}
                                control={control}
                                render={({ field }) => (
                                    <SelectAndSearchCombobox
                                        options={subjectOptions}
                                        label='Group/Subject'
                                        selectedValue={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />

                            <InputField
                                id={`academic-board-${index}`}
                                type='text'
                                label='Board/University'
                                register={register(`academic.${index}.board`)}
                            />

                            <Controller
                                name={`academic.${index}.grade`}
                                control={control}
                                render={({ field }) => (
                                    <SelectAndSearchCombobox
                                        options={gradeOptions}
                                        label='GPA/CGPA/Division'
                                        selectedValue={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />

                            <InputField
                                id={`academic-year-${index}`}
                                type='text'
                                label='Passing Year'
                                placeholder='e.g., 2023'
                                register={register(`academic.${index}.passingYear`)}
                            />
                        </div>
                    ))}

                    <AddNewFieldButton onClick={addAcademicField} />
                </div>

                {/* Proficiency Section */}
                <div>
                    <h3 className='pt-14 font-semibold text-xl leading-6 tracking-[0.4px] text-graySix mb-6'>Proficiency</h3>
                    {proficiencyFields.map((field, index) => (
                        <div key={field.id} className='p-4 border border-gray-200 rounded-lg mt-6'>
                            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                                <div className='flex items-center justify-between md:col-span-2'>
                                    <h4 className='font-medium text-gray-700'>Proficiency Test {index + 1}</h4>
                                    {proficiencyFields.length > 1 && (
                                        <button
                                            type="button"
                                            onClick={() => removeProficiency(index)}
                                            className="text-red-500 hover:text-red-700"
                                        >
                                            Remove
                                        </button>
                                    )}
                                </div>

                                <Controller
                                    name={`proficiency.${index}.nameOfExam`}
                                    control={control}
                                    render={({ field }) => (
                                        <SelectAndSearchCombobox
                                            options={proficiencyExamOptions}
                                            label='Name of Exam'
                                            selectedValue={field.value}
                                            onChange={field.onChange}
                                        />
                                    )}
                                />

                                <div>
                                    <InputField
                                        id={`proficiency-overall-${index}`}
                                        placeholder='Overall 0.0'
                                        className='placeholder:text-center'
                                        type='number'
                                        label='Overall Score'
                                        register={register(`proficiency.${index}.overall`, { valueAsNumber: true })}
                                    />
                                    <div className='grid grid-cols-4 gap-3 mt-2.5'>
                                        <InputField
                                            id={`proficiency-reading-${index}`}
                                            placeholder='R 0.0'
                                            className='placeholder:text-center'
                                            type='number'
                                            register={register(`proficiency.${index}.R`, { valueAsNumber: true })}
                                        />
                                        <InputField
                                            id={`proficiency-writing-${index}`}
                                            className='placeholder:text-center'
                                            placeholder='W 0.0'
                                            type='number'
                                            register={register(`proficiency.${index}.W`, { valueAsNumber: true })}
                                        />
                                        <InputField
                                            id={`proficiency-listening-${index}`}
                                            className='placeholder:text-center'
                                            placeholder='L 0.0'
                                            type='number'
                                            register={register(`proficiency.${index}.L`, { valueAsNumber: true })}
                                        />
                                        <InputField
                                            id={`proficiency-speaking-${index}`}
                                            className='placeholder:text-center'
                                            placeholder='S 0.0'
                                            type='number'
                                            register={register(`proficiency.${index}.S`, { valueAsNumber: true })}
                                        />
                                    </div>
                                </div>

                                <Controller
                                    name={`proficiency.${index}.examDate`}
                                    control={control}
                                    render={({ field }) => (
                                        <DatePicker
                                            value={field.value ? new Date(field.value) : undefined}
                                            onChange={field.onChange}
                                            title="Exam Date"
                                        />
                                    )}
                                />

                                <Controller
                                    name={`proficiency.${index}.expiryDate`}
                                    control={control}
                                    render={({ field }) => (
                                        <DatePicker
                                            value={field.value ? new Date(field.value) : undefined}
                                            onChange={field.onChange}
                                            title="Expiry Date"
                                        />
                                    )}
                                />
                            </div>
                            <div className='mt-6'>
                                <NoteTextarea
                                    register={register(`proficiency.${index}.note`)}
                                    placeholder="Additional notes about this proficiency test..."
                                />
                            </div>
                        </div>
                    ))}
                    <AddNewFieldButton onClick={addProficiencyField} />
                </div>

                {/* Publications Section */}
                <div>
                    <h3 className='pt-14 font-semibold text-xl leading-6 tracking-[0.4px] text-graySix mb-6'>Publications</h3>
                    {publicationFields.map((field, index) => (
                        <div key={field.id} className='grid grid-cols-1 md:grid-cols-2 gap-6 p-4 border border-gray-200 rounded-lg mt-6'>
                            <div className='md:col-span-2 flex items-center justify-between'>
                                <h4 className='font-medium text-gray-700'>Publication {index + 1}</h4>
                                {publicationFields.length > 1 && (
                                    <button
                                        type="button"
                                        onClick={() => removePublication(index)}
                                        className="text-red-500 hover:text-red-700"
                                    >
                                        Remove
                                    </button>
                                )}
                            </div>

                            <InputField
                                id={`publication-subject-${index}`}
                                placeholder='Subject'
                                type='text'
                                label='Subject'
                                register={register(`publications.${index}.subject`)}
                            />
                            <InputField
                                id={`publication-journal-${index}`}
                                placeholder='Journal'
                                type='text'
                                label='Journal'
                                register={register(`publications.${index}.journal`)}
                            />

                            <Controller
                                name={`publications.${index}.publicationDate`}
                                control={control}
                                render={({ field }) => (
                                    <DatePicker
                                        value={field.value ? new Date(field.value) : undefined}
                                        onChange={field.onChange}
                                        title="Publication Date"
                                    />
                                )}
                            />

                            <InputField
                                id={`publication-link-${index}`}
                                placeholder='Link'
                                type='text'
                                label='Link'
                                register={register(`publications.${index}.link`)}
                            />
                        </div>
                    ))}
                    <AddNewFieldButton onClick={addPublicationField} />
                </div>

                {/* Other Activities Section */}
                <div>
                    <h3 className='pt-14 font-semibold text-xl leading-6 tracking-[0.4px] text-graySix mb-6'>Other Activities</h3>
                    {activityFields.map((field, index) => (
                        <div key={field.id} className='grid grid-cols-1 md:grid-cols-2 gap-6 p-4 border border-gray-200 rounded-lg mt-6'>
                            <div className='md:col-span-2 flex items-center justify-between'>
                                <h4 className='font-medium text-gray-700'>Activity {index + 1}</h4>
                                {activityFields.length > 1 && (
                                    <button
                                        type="button"
                                        onClick={() => removeActivity(index)}
                                        className="text-red-500 hover:text-red-700"
                                    >
                                        Remove
                                    </button>
                                )}
                            </div>

                            <InputField
                                id={`activity-subject-${index}`}
                                placeholder='Subject/Field'
                                type='text'
                                label='Subject/Field'
                                register={register(`otherActivities.${index}.subject`)}
                            />

                            <InputField
                                id={`activity-link-${index}`}
                                placeholder='Certification Link'
                                type='text'
                                label='Certification Link'
                                register={register(`otherActivities.${index}.certificationLink`)}
                            />

                            <Controller
                                name={`otherActivities.${index}.startDate`}
                                control={control}
                                render={({ field }) => (
                                    <DatePicker
                                        value={field.value ? new Date(field.value) : undefined}
                                        onChange={field.onChange}
                                        title="Start Date"
                                    />
                                )}
                            />

                            <Controller
                                name={`otherActivities.${index}.endDate`}
                                control={control}
                                render={({ field }) => (
                                    <DatePicker
                                        value={field.value ? new Date(field.value) : undefined}
                                        onChange={field.onChange}
                                        title="End Date"
                                    />
                                )}
                            />
                        </div>
                    ))}
                    <AddNewFieldButton onClick={addActivityField} />
                </div>
            </form>
        </SectionLayout>
    );
});

Education.displayName = 'Education';

export default Education;
