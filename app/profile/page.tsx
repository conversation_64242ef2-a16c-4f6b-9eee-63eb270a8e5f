'use client';

import React, { useState } from 'react';
import Tab from '@/app/components/Tab';
import Heading from '@/app/components/Heading';
import EditButton from '@/app/components/EditButton';
import StudentProfile from '@/app/components/StudentProfile';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';

const page = () => {
    const [activeTab, setActiveTab] = useState<number>(1);
    const [editMode, setEditMode] = useState<boolean>(false);
    const currentStudent = useSelector((state: RootState) => state.student.currentStudent);
    const hasStudentData = currentStudent !== null;

    const getHeadingByTab = (tabId: number) => {
        switch (tabId) {
            case 1:
                return 'My Profile';
            case 2:
                return 'Student Education';
            case 3:
                return 'Student Application Info';
            case 4:
                return 'Student Payment';
            case 5:
                return 'Student Documents';
            default:
                return 'My Profile';
        }
    };

    const handleTabChange = (tabId: number) => {
        setActiveTab(tabId);
        setEditMode(false); // Reset edit mode when changing tabs
    };

    const handleEditClick = () => {
        if (activeTab === 1) { // Only allow edit for Personal tab
            setEditMode(true);
        }
    };

    const handleEditModeChange = (mode: boolean) => {
        setEditMode(mode);
    };

    return (
        <DashboardLayout>
            <div className='flex justify-between pt-10 pb-5'>
                <div>
                    <Heading level='h1'>
                        {getHeadingByTab(activeTab)}
                    </Heading>
                </div>
                <div>
                    {activeTab === 1 && !editMode && (
                        <EditButton className='bg-white' onClick={handleEditClick} />
                    )}
                </div>
            </div>

            {/* Show StudentProfile only for Personal tab and when not in edit mode */}
            {activeTab === 1 && !editMode && hasStudentData && (
                <div className='mb-5'>
                    <StudentProfile />
                </div>
            )}

            <Tab
                onTabChange={handleTabChange}
                editMode={editMode}
                onEditModeChange={handleEditModeChange}
            />
        </DashboardLayout>
    )
}

export default page