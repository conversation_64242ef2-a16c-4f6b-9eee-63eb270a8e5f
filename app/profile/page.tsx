'use client';

import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import Tab from '@/app/components/Tab';
import Heading from '@/app/components/Heading';
import EditButton from '@/app/components/EditButton';
import StudentProfile from '@/app/components/StudentProfile';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    // Get student data from Redux store
    const currentStudent = useSelector((state: RootState) => state.student.currentStudent);
    const hasStudentData = currentStudent !== null;

    return (
        <DashboardLayout>
            <div className='flex justify-between pt-10 pb-5'>
                <div>
                    <Heading level='h1'>
                        My Profile
                    </Heading>
                </div>
                <div>
                    <EditButton className='bg-white' />
                </div>
            </div>

            {hasStudentData && (
                <div className='mb-5'>
                    <StudentProfile />
                </div>
            )}
            <Tab />
        </DashboardLayout>
    )
}

export default page