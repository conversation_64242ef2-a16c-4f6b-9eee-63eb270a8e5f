'use client';

import React, { useState } from 'react';
import Tab from '@/app/components/Tab';
import Heading from '@/app/components/Heading';
import EditButton from '@/app/components/EditButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    const [selectedTab, setSelectedTab] = useState(1);

    const generateHeader = () => {
        if (selectedTab === 1) {
            return 'My Profile';
        } else if (selectedTab === 2) {
            return 'Student Education';
        } else if (selectedTab === 3) {
            return 'Student Application Info ';
        } else if (selectedTab === 4) {
            return 'Student Payment';
        } else if (selectedTab === 5) {
            return 'Student Documents';
        }
    };

    return (
        <DashboardLayout>
            <div className='flex justify-between pt-10 pb-5'>
                <div>
                    <Heading level='h1'>
                        {generateHeader()}
                    </Heading>
                </div>
                <div>
                    <EditButton className='bg-white' />
                </div>
            </div>
            <Tab setSelectedTab={setSelectedTab} />
        </DashboardLayout>
    )
}

export default page