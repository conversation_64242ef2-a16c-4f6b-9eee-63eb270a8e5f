import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    reactStrictMode: false,
    // plugins: [require('tailwind-scrollbar-hide')],
    // output: 'export',
    trailingSlash: true,
    images: {
        domains: ['lh3.googleusercontent.com','images.pexels.com'], 
        // loader: 'custom',
        // loaderFile: './loader.ts',
    },
    // async rewrites() {
    //     return [
    //         {
    //             source: '/api/auth/:path*',
    //             destination: 'http://auth-api.localhost/api/auth/:path*',
    //         },
    //     ];
    // },
};

export default nextConfig;
